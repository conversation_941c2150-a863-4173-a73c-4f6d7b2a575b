'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ColumnDef } from '@tanstack/react-table'
import { Edit, MoreHorizontal, Power, PowerOff, Trash2, Users } from 'lucide-react'
import type { OrganizationPlan } from '../page'

interface ColumnActions {
  onEdit?: (plan: OrganizationPlan) => void
  onToggleStatus?: (plan: OrganizationPlan) => void
  onDelete?: (plan: OrganizationPlan) => void
}

export const createColumns = (actions?: ColumnActions): ColumnDef<OrganizationPlan>[] => [
  {
    accessorKey: 'name',
    header: 'Tên gói',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <div>
          <div className="font-medium">{plan.name}</div>
          {plan.description && <div className="text-muted-foreground text-xs">{plan.description}</div>}
        </div>
      )
    },
  },
  {
    accessorKey: 'features',
    header: 'T<PERSON>h năng',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <div className="flex flex-wrap gap-1">
          {plan.features.map((feature) => (
            <Badge
              key={feature.id}
              variant="outline"
              className="text-xs">
              {feature.name}: {feature.pivot.charges === null ? 'Không giới hạn' : `${feature.pivot.charges}`}
            </Badge>
          ))}
        </div>
      )
    },
  },
  {
    accessorKey: 'periodicity',
    header: 'Chu kỳ',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <div className="text-muted-foreground text-sm">
          {plan.periodicity} {plan.periodicity_type} • Gia hạn: {plan.grace_days} ngày
        </div>
      )
    },
  },
  {
    accessorKey: 'price',
    header: 'Giá',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <div className="text-sm font-medium">
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(plan.price)}
        </div>
      )
    },
  },
  {
    accessorKey: 'subscriptions',
    header: 'Thành viên',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <Badge variant="secondary">
          <Users className="mr-1 h-3 w-3" />
          {plan.subscriptions?.length || 0} thành viên
        </Badge>
      )
    },
  },
  {
    accessorKey: 'is_active',
    header: 'Trạng thái',
    cell: ({ row }) => {
      const plan = row.original
      return (
        <Badge variant={plan.is_active ? 'default' : 'secondary'}>{plan.is_active ? 'Hoạt động' : 'Vô hiệu hóa'}</Badge>
      )
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const plan = row.original
      const isActive = plan.is_active

      return (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0">
                <span className="sr-only">Mở menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => actions?.onEdit?.(plan)}>
                <Edit className="size-4" />
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => actions?.onToggleStatus?.(plan)}>
                {isActive ? (
                  <>
                    <PowerOff className="size-4" />
                    Vô hiệu hóa
                  </>
                ) : (
                  <>
                    <Power className="size-4" />
                    Kích hoạt
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                variant="destructive"
                onClick={() => actions?.onDelete?.(plan)}>
                <Trash2 className="size-4" />
                Xóa
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  },
]

// Export default columns without actions for backward compatibility
export const columns = createColumns()
