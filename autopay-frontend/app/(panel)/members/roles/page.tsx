'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { buildQueryString } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { formatDistanceToNow } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Crown, Edit, Eye, MoreHorizontal, Shield, Trash2, Users } from 'lucide-react'
import { useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback } from 'usehooks-ts'

// Role type definition
interface Role {
  id: string
  name: string
  description?: string
  is_default?: boolean
  permissions_count?: number
  users_count?: number
  created_at?: string
  permissions?: Array<{
    id: string
    name: string
  }>
}

// Define columns for the datatable
const columns: ColumnDef<Role>[] = [
  {
    accessorKey: 'name',
    header: 'Vai trò',
    cell: ({ row }) => {
      const role = row.original
      return (
        <div className="flex items-center space-x-3">
          <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
            <Shield className="text-primary h-4 w-4" />
          </div>
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="font-medium">{role.name}</span>
              {role.is_default && <Crown className="h-4 w-4 text-yellow-500" />}
            </div>
            {role.description && <span className="text-muted-foreground text-sm">{role.description}</span>}
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'users_count',
    header: 'Thành viên',
    cell: ({ row }) => {
      const count = row.getValue('users_count') as number
      return (
        <Badge variant="secondary">
          <Users className="mr-1 h-3 w-3" />
          {count || 0} thành viên
        </Badge>
      )
    },
  },
  {
    accessorKey: 'permissions_count',
    header: 'Quyền hạn',
    cell: ({ row }) => {
      const count = row.getValue('permissions_count') as number
      return <Badge variant="outline">{count || 0} quyền</Badge>
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Tạo lúc',
    cell: ({ row }) => {
      const createdAt = row.getValue('created_at') as string
      if (!createdAt) return <span className="text-muted-foreground">-</span>

      try {
        const date = new Date(createdAt)
        return <span className="text-sm">{formatDistanceToNow(date, { addSuffix: true, locale: vi })}</span>
      } catch {
        return <span className="text-muted-foreground">-</span>
      }
    },
  },
  {
    id: 'actions',
    header: () => <div className="text-right"></div>,
    cell: ({ row }) => {
      const role = row.original
      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Eye className="size-4" />
                Xem chi tiết
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="size-4" />
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Users className="size-4" />
                Quản lý thành viên
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {!role.is_default && (
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="size-4" />
                  Xóa vai trò
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  },
]

export default function RolesPage() {
  const { user } = useUser()
  const [search, setSearch] = useState('')
  const [inputValue, setInputValue] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)

  const { isPending, error, data } = useQuery({
    queryKey: ['getOrganizationRoles', search, user?.current_organization?.alias],
    queryFn: async () => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      const queryString = buildQueryString({
        filter: {
          name: search,
        },
      })

      return await queryFetchHelper(`/${user.current_organization.alias}/roles?${queryString}`)
    },
    enabled: !!user?.current_organization?.alias,
  })

  if (error) {
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Phân quyền</h3>
          <p className="text-muted-foreground text-sm">Quản lý vai trò và quyền hạn của thành viên.</p>
        </div>
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Lỗi!</AlertTitle>
          <AlertDescription>Không thể tải danh sách vai trò. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      </div>
    )
  }

  const roles = data?.data?.data || []

  // Filter roles based on search
  const filteredRoles = roles.filter((role: Role) => {
    // If there's no input value, show all roles
    if (!inputValue) return true

    // If search query matches input value, show all roles (server already filtered)
    if (search === inputValue) return true

    // Otherwise, filter client-side only if no server search is pending
    if (!search) {
      return (
        role.name.toLowerCase().includes(inputValue.toLowerCase()) ||
        (role.description && role.description.toLowerCase().includes(inputValue.toLowerCase()))
      )
    }

    // If server search is different from input, show all roles (wait for server)
    return true
  })

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Phân quyền</h3>
          <p className="text-muted-foreground text-sm">Quản lý vai trò và quyền hạn của thành viên.</p>
        </div>
        <div className="flex items-center space-x-2">
          <Input
            type="search"
            placeholder="Tìm kiếm vai trò..."
            className="w-[200px]"
            value={inputValue}
            onChange={(event) => {
              setInputValue(event.target.value)
              debounced(event.target.value)
            }}
          />
          <Button>
            <FiPlusCircle className="size-4" />
            Tạo vai trò
          </Button>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={filteredRoles}
        isLoading={isPending}
        emptyMessage={
          inputValue && search === inputValue
            ? `Không tìm thấy vai trò với từ khóa "${inputValue}"`
            : inputValue && search !== inputValue
              ? 'Đang tìm kiếm...'
              : 'Chưa có vai trò nào trong tổ chức'
        }
      />
    </div>
  )
}
