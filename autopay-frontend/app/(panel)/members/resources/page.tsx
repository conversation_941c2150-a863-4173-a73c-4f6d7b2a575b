'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import { <PERSON>ert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { Database, Globe, MoreHorizontal, Server, Settings, Users } from 'lucide-react'
import { useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { useDebounceCallback } from 'usehooks-ts'

// Resource type definition
interface Resource {
  resource_id: string
  resource_type: string
  resource_type_name: string
  resource: {
    id: string
    name: string
    [key: string]: any
  }
  managing_teams: Array<{
    id: string
    name: string
    assigned_at: string
  }>
}

// Helper function to get resource icon
const getResourceIcon = (resourceType: string) => {
  switch (resourceType) {
    case 'server':
      return <Server className="h-4 w-4" />
    case 'webapp':
      return <Globe className="h-4 w-4" />
    case 'database':
      return <Database className="h-4 w-4" />
    default:
      return <Settings className="h-4 w-4" />
  }
}

// Helper function to get resource type display name
const getResourceTypeDisplayName = (resourceType: string) => {
  switch (resourceType) {
    case 'server':
      return 'Server'
    case 'webapp':
      return 'Webapp'
    case 'database':
      return 'Database'
    default:
      return resourceType.charAt(0).toUpperCase() + resourceType.slice(1)
  }
}

// Define columns for the datatable
const columns: ColumnDef<Resource>[] = [
  {
    accessorKey: 'resource.name',
    header: 'Tên Resource',
    cell: ({ row }) => {
      const resource = row.original
      return (
        <div className="flex items-center space-x-3">
          <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
            {getResourceIcon(resource.resource_type_name || 'unknown')}
          </div>
          <div className="flex flex-col">
            <span className="font-medium">{resource.resource?.name || 'Unknown Resource'}</span>
            <span className="text-muted-foreground text-sm">
              {getResourceTypeDisplayName(resource.resource_type_name || 'unknown')}
            </span>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'managing_teams',
    header: 'Teams quản lý',
    cell: ({ row }) => {
      const teams = row.getValue('managing_teams') as Resource['managing_teams']
      if (!teams || !Array.isArray(teams) || teams.length === 0) {
        return <span className="text-muted-foreground text-sm">Không có team nào</span>
      }
      return (
        <div className="flex flex-wrap gap-1">
          {teams.map((team) => (
            <Badge
              key={team.id}
              variant="secondary">
              <Users className="mr-1 h-3 w-3" />
              {team.name}
            </Badge>
          ))}
        </div>
      )
    },
  },
  {
    accessorKey: 'resource_type_name',
    header: 'Loại',
    cell: ({ row }) => {
      const resourceType = row.getValue('resource_type_name') as string
      return <Badge variant="outline">{getResourceTypeDisplayName(resourceType)}</Badge>
    },
  },
  {
    id: 'actions',
    header: () => <div className="text-right"></div>,
    cell: () => {
      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Settings className="size-4" />
                Xem chi tiết
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Users className="size-4" />
                Quản lý teams
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  },
]

export default function ResourcesPage() {
  const { user } = useUser()
  const [search, setSearch] = useState('')
  const [inputValue, setInputValue] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)

  const { isPending, error, data } = useQuery({
    queryKey: ['getOrganizationResources', user?.current_organization?.alias],
    queryFn: async () => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      return await queryFetchHelper(`/${user.current_organization.alias}/resources`)
    },
    enabled: !!user?.current_organization?.alias,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  if (error) {
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Resources</h3>
          <p className="text-muted-foreground text-sm">Quản lý tất cả resources của tổ chức và teams đang quản lý.</p>
        </div>
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Lỗi!</AlertTitle>
          <AlertDescription>Không thể tải danh sách resources. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      </div>
    )
  }

  // Safely extract resources from API response
  let resources: Resource[] = []
  const apiData = data as any

  if (Array.isArray(apiData?.data)) {
    resources = apiData.data
  } else if (apiData?.data?.values && Array.isArray(apiData.data.values)) {
    resources = apiData.data.values
  }

  // Filter resources based on search
  let filteredResources: Resource[] = []

  try {
    if (Array.isArray(resources)) {
      filteredResources = resources.filter((resource: Resource) => {
        // If there's no input value, show all resources
        if (!inputValue) return true

        // Filter client-side based on resource name and teams
        return (
          resource.resource?.name?.toLowerCase().includes(inputValue.toLowerCase()) ||
          resource.resource_type_name?.toLowerCase().includes(inputValue.toLowerCase()) ||
          resource.managing_teams?.some((team) => team.name?.toLowerCase().includes(inputValue.toLowerCase()))
        )
      })
    } else {
      console.error('Resources is not an array:', resources)
      filteredResources = []
    }
  } catch (error) {
    console.error('Error filtering resources:', error)
    filteredResources = []
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Resources</h3>
          <p className="text-muted-foreground text-sm">Quản lý tất cả resources của tổ chức và teams đang quản lý.</p>
        </div>
        <div className="flex items-center space-x-2">
          <Input
            type="search"
            placeholder="Tìm kiếm resources..."
            className="w-[200px]"
            value={inputValue}
            onChange={(event) => {
              setInputValue(event.target.value)
              debounced(event.target.value)
            }}
          />
        </div>
      </div>

      <DataTable
        columns={columns}
        data={filteredResources}
        isLoading={isPending}
        emptyMessage={
          inputValue
            ? `Không tìm thấy resource với từ khóa "${inputValue}"`
            : resources.length === 0
              ? 'Chưa có resource nào được assign cho teams trong tổ chức'
              : 'Chưa có resource nào trong tổ chức'
        }
      />
    </div>
  )
}
