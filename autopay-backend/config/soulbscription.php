<?php

return [
    'database' => [
        'cancel_migrations_autoloading' => true,
    ],
    /*
    |--------------------------------------------------------------------------
    | Models Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the models used by the Soulbscription package.
    | You can override the default models with your own custom models.
    |
    */
    'models' => [
        'feature' => \Modules\Subscription\Models\Feature::class,
        'feature_consumption' => \Modules\Subscription\Models\FeatureConsumption::class,
        'feature_ticket' => \Modules\Subscription\Models\FeatureTicket::class,
        'feature_plan' => \Modules\Subscription\Models\FeaturePlan::class,
        'plan' => \Modules\Subscription\Models\Plan::class,
        'subscription' => \Modules\Subscription\Models\Subscription::class,
        'subscription_renewal' => \Modules\Subscription\Models\SubscriptionRenewal::class,

        'subscriber' => [
            'model' => \Modules\User\Models\User::class,
            'uses_uuid' => false, // Using ULID instead
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Table Names
    |--------------------------------------------------------------------------
    |
    | Here you can configure the table names used by the package.
    |
    */
    'table_names' => [
        'plans' => 'plans',
        'features' => 'features',
        'subscriptions' => 'subscriptions',
        'feature_consumptions' => 'feature_consumptions',
        'feature_tickets' => 'feature_tickets',
        'subscription_renewals' => 'subscription_renewals',
        'feature_plan' => 'feature_plan',
    ],
];
