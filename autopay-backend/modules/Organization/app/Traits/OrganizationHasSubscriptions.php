<?php

namespace Modules\Organization\Traits;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\Subscription\Models\Subscription;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Feature;
use Modules\Subscription\Models\FeatureConsumption;
use Modules\Subscription\Models\FeatureTicket;

/**
 * Trait for Organization to have subscription capabilities
 *
 * Allows organizations to subscribe to system plans (like pay-as-you-go)
 * for organization-level billing and usage tracking
 */
trait OrganizationHasSubscriptions
{
    /**
     * Get all subscriptions for this organization
     */
    public function subscriptions(): MorphMany
    {
        return $this->morphMany(Subscription::class, 'subscriber');
    }

    /**
     * Get active subscriptions for this organization
     */
    public function activeSubscriptions()
    {
        return $this->subscriptions()->notCanceled();
    }

    /**
     * Get feature consumptions for this organization
     */
    public function featureConsumptions(): MorphMany
    {
        return $this->morphMany(FeatureConsumption::class, 'subscriber');
    }

    /**
     * Get feature tickets for this organization
     */
    public function featureTickets(): MorphMany
    {
        return $this->morphMany(FeatureTicket::class, 'subscriber');
    }

    /**
     * Subscribe organization to a plan
     */
    public function subscribeTo(Plan $plan, ?string $startDate = null): Subscription
    {
        // Only allow subscription to system plans
        if ($plan->isOrganizationPlan()) {
            throw new \InvalidArgumentException('Organizations can only subscribe to system plans');
        }

        // Cancel any existing active subscriptions
        $this->activeSubscriptions()->each(function ($subscription) {
            $subscription->cancel();
        });

        // Create new subscription
        return $this->subscriptions()->create([
            'plan_id' => $plan->id,
            'started_at' => $startDate ?? now()->toDateString(),
        ]);
    }

    /**
     * Check if organization has an active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscriptions()->exists();
    }

    /**
     * Get current active subscription
     */
    public function currentSubscription(): ?Subscription
    {
        return $this->activeSubscriptions()->first();
    }

    /**
     * Check if organization can consume a feature
     */
    public function canConsume(Feature $feature, float $consumption = 1): bool
    {
        $activeSubscription = $this->currentSubscription();

        if (!$activeSubscription) {
            return false;
        }

        return $activeSubscription->canConsume($feature, $consumption);
    }

    /**
     * Consume a feature for the organization
     */
    public function consume(Feature $feature, float $consumption = 1): FeatureConsumption
    {
        $activeSubscription = $this->currentSubscription();

        if (!$activeSubscription) {
            throw new \RuntimeException('Organization has no active subscription');
        }

        if (!$this->canConsume($feature, $consumption)) {
            throw new \RuntimeException('Cannot consume feature: quota exceeded or feature not available');
        }

        return $this->featureConsumptions()->create([
            'feature_id' => $feature->id,
            'consumption' => $consumption,
            'expired_at' => $feature->getResetDate(),
        ]);
    }

    /**
     * Get remaining quota for a feature
     */
    public function getRemainingQuota(Feature $feature): ?float
    {
        $activeSubscription = $this->currentSubscription();

        if (!$activeSubscription) {
            return null;
        }

        $planFeature = $activeSubscription->plan->features()
            ->where('feature_id', $feature->id)
            ->first();

        if (!$planFeature) {
            return null;
        }

        $charges = $planFeature->pivot->charges;

        if ($charges === null) {
            return null; // Unlimited
        }

        $consumed = $this->getConsumedQuota($feature);

        return max(0, $charges - $consumed);
    }

    /**
     * Get consumed quota for a feature in current period
     */
    public function getConsumedQuota(Feature $feature): float
    {
        $resetDate = $feature->getResetDate();

        return $this->featureConsumptions()
            ->where('feature_id', $feature->id)
            ->where('created_at', '>=', $resetDate)
            ->sum('consumption');
    }

    /**
     * Get usage statistics for all features
     */
    public function getUsageStats(): array
    {
        $activeSubscription = $this->currentSubscription();

        if (!$activeSubscription) {
            return [];
        }

        $stats = [];

        foreach ($activeSubscription->plan->features as $feature) {
            $consumed = $this->getConsumedQuota($feature);
            $remaining = $this->getRemainingQuota($feature);
            $quota = $feature->pivot->charges;

            $stats[] = [
                'feature_id' => $feature->id,
                'feature_name' => $feature->name,
                'consumed' => $consumed,
                'remaining' => $remaining,
                'quota' => $quota,
                'is_unlimited' => $quota === null,
                'usage_percentage' => $quota ? ($consumed / $quota) * 100 : 0,
            ];
        }

        return $stats;
    }

    /**
     * Check if organization is on pay-as-you-go plan
     */
    public function isOnPayAsYouGo(): bool
    {
        $activeSubscription = $this->currentSubscription();

        if (!$activeSubscription) {
            return false;
        }

        return $activeSubscription->plan->name === 'pay-as-you-go';
    }

    /**
     * Get total billable usage for current period
     */
    public function getBillableUsage(): array
    {
        if (!$this->isOnPayAsYouGo()) {
            return [];
        }

        $usage = [];
        $activeSubscription = $this->currentSubscription();

        foreach ($activeSubscription->plan->features as $feature) {
            $consumed = $this->getConsumedQuota($feature);

            $usage[] = [
                'feature_id' => $feature->id,
                'feature_name' => $feature->name,
                'consumed' => $consumed,
                'rate' => $feature->pivot->charges ?? 0,
                'total_cost' => $consumed * ($feature->pivot->charges ?? 0),
            ];
        }

        return $usage;
    }
}
