<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert default bank settings
        $settings = [
            // OCB Bank Settings
            ['group' => 'bank', 'name' => 'ocb_access_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'ocb_token_expires_at', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'ocb_refresh_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'ocb_token_requested_at', 'payload' => json_encode(null), 'locked' => false],

            // MB Bank Settings
            ['group' => 'bank', 'name' => 'mb_access_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'mb_token_expires_at', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'mb_refresh_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'mb_token_requested_at', 'payload' => json_encode(null), 'locked' => false],

            // KLB Bank Settings
            ['group' => 'bank', 'name' => 'klb_access_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'klb_token_expires_at', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'klb_refresh_token', 'payload' => json_encode(null), 'locked' => false],
            ['group' => 'bank', 'name' => 'klb_token_requested_at', 'payload' => json_encode(null), 'locked' => false],
        ];

        foreach ($settings as $setting) {
            DB::table('settings')->updateOrInsert(
                ['group' => $setting['group'], 'name' => $setting['name']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove bank settings
        DB::table('settings')->where('group', 'bank')->delete();
    }
};
