<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Modules\Bank\Integrations\Mb\MbBaseRequest;
use Illuminate\Support\Str;

class ValidateVirtualAccountRequest extends MbBaseRequest
{
    public function __construct(public string $customerAcc)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/validate-virtual-account';
    }

    /**
     * Validate virtual account and return response
     */
    public function validateAccount(): array
    {
        try {
            // Verify signature
            $verify = $this->verifyCustomerAccount($this->customerAcc);

            if ($verify) {
                $signature = $this->createSignature($this->customerAcc);

                return [
                    'success' => true,
                    'requestId' => strtolower(Str::ulid()),
                    'data' => [
                        'customerAcc' => $this->customerAcc,
                        'customerName' => 'AUTOPAY',
                        'responseCode' => '00',
                        'responseDesc' => 'Thành công',
                    ],
                    'signature' => $signature,
                ];
            }

            return [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'customerAcc' => $this->customerAcc,
                    'customerName' => 'Không tìm thấy khách hàng',
                    'responseDesc' => 'Không tìm thấy khách hàng',
                    'responseCode' => '01',
                ],
                'signature' => $this->createSignature($this->customerAcc),
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xác thực tài khoản ảo MBBank.',
                'error_code' => 'VALIDATION_ERROR',
            ];
        }
    }

    /**
     * Verify customer account signature
     */
    protected function verifyCustomerAccount(string $customerAcc): bool
    {
        // In real implementation, this would verify against MBBank signature
        // For now, return true for valid format
        return strlen($customerAcc) >= 8 && strlen($customerAcc) <= 19;
    }
}
