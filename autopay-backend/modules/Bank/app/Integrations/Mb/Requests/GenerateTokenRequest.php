<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class GenerateTokenRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function resolveEndpoint(): string
    {
        return '/token-generate';
    }

    protected function defaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . config('bank.supported_banks.mb.api_config.auth_token'),
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [];
    }
}
