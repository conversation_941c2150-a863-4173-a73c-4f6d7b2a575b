<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Modules\Bank\Integrations\Mb\MbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class CreateVirtualAccountRequest extends MbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public array $virtualAccountData)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/va-account';
    }

    protected function defaultHeaders(): array
    {
        $customerAcc = $this->virtualAccountData['customer_acc'] ?? '';
        
        return [
            'signature' => $this->createSignature($customerAcc),
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'customerAcc' => $this->virtualAccountData['customer_acc'] ?? '',
        ];
    }
}
