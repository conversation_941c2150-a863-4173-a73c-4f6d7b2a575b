<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Modules\Bank\Integrations\Mb\MbBaseRequest;
use Illuminate\Support\Str;

class SyncVirtualAccountTransactionRequest extends MbBaseRequest
{
    public function __construct(public array $transactionData)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/sync-virtual-account-transaction';
    }

    /**
     * Sync virtual account transaction and return response
     */
    public function syncTransaction(): array
    {
        try {
            $signatureData = $this->transactionData['referenceNumber'] .
                           $this->transactionData['customerAcc'] .
                           $this->transactionData['amount'] .
                           $this->transactionData['transDate'];

            $verify = $this->verifySignature($signatureData, $this->transactionData['signature']);
            $transactionId = strtolower(Str::ulid());

            if ($verify) {
                $responseCode = '00';
                $signature = $this->createTransactionSignature($transactionId . $responseCode);

                return [
                    'success' => true,
                    'requestId' => strtolower(Str::ulid()),
                    'data' => [
                        'transactionId' => $transactionId,
                        'responseCode' => $responseCode,
                        'responseDesc' => 'Successful',
                    ],
                    'signature' => $signature,
                ];
            }

            $responseCode = '01';
            $signature = $this->createTransactionSignature($transactionId . $responseCode);

            return [
                'success' => false,
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'transactionId' => $transactionId,
                    'responseCode' => $responseCode,
                    'responseDesc' => 'Failed',
                ],
                'signature' => $signature,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                'error_code' => 'SYNC_ERROR',
            ];
        }
    }

    /**
     * Create transaction signature
     */
    protected function createTransactionSignature(string $data): string
    {
        $privateKey = config('bank.supported_banks.mb.api_config.autopay_private_key');
        openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }
}
