<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Modules\Bank\Integrations\Mb\MbBaseRequest;
use Illuminate\Support\Str;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class GetVirtualAccountTransactionsRequest extends MbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public array $filters)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/va-transaction-sync';
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return once(function () {
            $requestData = [
                'requestId' => strtolower(Str::ulid()),
                'data' => [
                    'referenceNumber' => $this->filters['reference_number'] ?? '',
                    'amount' => $this->filters['amount'] ?? 0,
                    'customerAcc' => $this->filters['customer_acc'] ?? '',
                    'transDate' => $this->filters['trans_date'] ?? now()->format('Y-m-d H:i:s'),
                    'billNumber' => $this->filters['bill_number'] ?? '',
                    'endPointUrl' => $this->filters['endpoint_url'] ?? null,
                    'userName' => $this->filters['user_name'] ?? null,
                    'rate' => $this->filters['rate'] ?? null,
                    'customerName' => $this->filters['customer_name'] ?? null,
                    'additionalData' => $this->filters['additional_data'] ?? [],
                ],
            ];

            // Create signature for transaction data
            $signatureData = $requestData['data']['referenceNumber'] .
                           $requestData['data']['customerAcc'] .
                           $requestData['data']['amount'] .
                           $requestData['data']['transDate'];

            $requestData['signature'] = $this->createSignature($signatureData);

            return $requestData;
        });
    }
}
