<?php

namespace Modules\Bank\Integrations\Mb\Requests;

use Modules\Bank\Integrations\Mb\MbBaseRequest;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Traits\Body\HasJsonBody;

class CheckAccountRequest extends MbBaseRequest implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(public string $accountNumber)
    {
    }

    public function resolveEndpoint(): string
    {
        return '/va-account';
    }

    protected function defaultHeaders(): array
    {
        return [
            'signature' => $this->createSignature($this->accountNumber),
            'Content-Type' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'customerAcc' => $this->accountNumber,
        ];
    }
}
