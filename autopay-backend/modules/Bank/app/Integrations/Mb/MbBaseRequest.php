<?php

namespace Modules\Bank\Integrations\Mb;

use Saloon\Http\Request;

abstract class MbBaseRequest extends Request
{
    /**
     * Create RSA signature for data
     */
    protected function createSignature(string $data): string
    {
        $privateKey = config('bank.supported_banks.mb.api_config.autopay_private_key');
        openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }

    /**
     * Verify RSA signature from MBBank
     */
    protected function verifySignature(string $data, string $signature): bool
    {
        $publicKey = config('bank.supported_banks.mb.api_config.mb_public_key');
        $verify = openssl_verify($data, base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA256);

        return $verify === 1;
    }
}
