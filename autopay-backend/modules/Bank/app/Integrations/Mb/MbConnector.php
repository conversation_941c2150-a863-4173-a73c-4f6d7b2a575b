<?php

namespace Modules\Bank\Integrations\Mb;

use App\Settings\BankSettings;
use Illuminate\Support\Carbon;
use JsonException;
use Modules\Bank\Integrations\Mb\Requests\GenerateTokenRequest;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Http\Auth\TokenAuthenticator;
use Saloon\Http\Connector;
use Saloon\Http\Response;
use Saloon\Traits\Plugins\AcceptsJson;

class MbConnector extends Connector
{
    use AcceptsJson;

    public ?int $tries = 2;

    // Checking for requesting class to avoid infinite looping for token request class
    private ?string $currentRequestClass = null;

    protected BankSettings $settings;

    public function __construct()
    {
        $this->settings = app(BankSettings::class);
    }

    public function resolveBaseUrl(): string
    {
        return config('bank.supported_banks.mb.api_config.base_url', '');
    }

    public function hasRequestFailed(Response $response): ?bool
    {
        // Check if the response is a 401 Unauthorized
        if ($response->status() === 401) {
            $this->settings->clearTokens('mb');
        }

        return $response->status() >= 500;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    public function getAccessToken(): string
    {
        $token = $this->settings->getAccessToken('mb');

        if (!$token || $this->settings->isTokenExpired('mb')) {
            $this->currentRequestClass = GenerateTokenRequest::class;
            $response = $this->send(new GenerateTokenRequest);
            $this->currentRequestClass = null;

            if ($response->failed()) {
                throw new \Exception('Không thể gửi yêu cầu lấy access token với ngân hàng MBBank, vui lòng liên hệ với bộ phận hỗ trợ.');
            }

            $responseData = $response->array();
            $token = $responseData['access_token'];
            $expiresAt = now()->addSeconds($responseData['expires_in'] - 60)->toDateTimeString();

            $this->settings->setAccessToken('mb', $token, $expiresAt);
        }

        return $token;
    }

    /**
     * @throws FatalRequestException
     * @throws RequestException
     * @throws JsonException
     */
    protected function defaultAuth(): TokenAuthenticator
    {
        return new TokenAuthenticator($this->currentRequestClass !== GenerateTokenRequest::class ? $this->getAccessToken() : '');
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }
}
