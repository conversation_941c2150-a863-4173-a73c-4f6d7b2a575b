<?php

namespace Modules\Subscription\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Subscription\Models\Plan;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing Organization and Member Subscriptions
 */
class OrganizationSubscriptionController extends Controller
{

    /**
     * Subscribe organization to a system plan
     */
    public function subscribeOrganization(Organization $organization, Request $request): Response
    {
        $request->validate([
            'plan_id' => ['required', 'string', 'exists:plans,id'],
        ]);

        $plan = Plan::findOrFail($request->plan_id);

        // Ensure it's a system plan
        if ($plan->isOrganizationPlan()) {
            return ResponseHelper::error(
                message: 'Organizations can only subscribe to system plans',
                httpCode: 422
            );
        }

        try {
            $subscription = $organization->subscribeTo($plan);

            return ResponseHelper::success(
                message: 'Organization subscribed successfully',
                data: $subscription->load('plan')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to subscribe: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Get organization's current subscription
     */
    public function organizationSubscription(Organization $organization): Response
    {
        $subscription = $organization->currentSubscription();

        if (!$subscription) {
            return ResponseHelper::error(
                message: 'No active subscription found',
                httpCode: 404
            );
        }

        return ResponseHelper::success(data: $subscription->load('plan'));
    }

    /**
     * Get organization's usage statistics
     */
    public function organizationUsage(Organization $organization): Response
    {
        $usageStats = $organization->getUsageStats();

        return ResponseHelper::success(data: [
            'usage_stats' => $usageStats,
            'is_pay_as_you_go' => $organization->isOnPayAsYouGo(),
            'billable_usage' => $organization->getBillableUsage(),
        ]);
    }

    /**
     * Subscribe a user to an organization plan
     */
    public function subscribeUser(Organization $organization, Request $request): Response
    {
        $request->validate([
            'user_id' => ['required', 'string', 'exists:users,id'],
            'plan_id' => ['required', 'string', 'exists:plans,id'],
        ]);

        $user = User::findOrFail($request->user_id);
        $plan = Plan::findOrFail($request->plan_id);

        // Ensure plan belongs to organization or is a system plan
        if ($plan->isOrganizationPlan() && $plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not available for this organization',
                httpCode: 422
            );
        }

        // Check if user belongs to organization
        if (!$organization->hasMember($user)) {
            return ResponseHelper::error(
                message: 'User is not a member of this organization',
                httpCode: 422
            );
        }

        try {
            // Cancel any existing active subscriptions for this user
            $user->subscriptions()->notCanceled()->each(function ($subscription) {
                $subscription->cancel();
            });

            // Create new subscription
            $subscription = $user->subscribeTo($plan);

            return ResponseHelper::success(
                message: 'User subscribed successfully',
                data: $subscription->load('plan')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to subscribe user: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Cancel organization subscription
     */
    public function cancelOrganizationSubscription(Organization $organization): Response
    {
        $subscription = $organization->currentSubscription();

        if (!$subscription) {
            return ResponseHelper::error(
                message: 'No active subscription found',
                httpCode: 404
            );
        }

        try {
            $subscription->cancel();

            return ResponseHelper::success(
                message: 'Subscription canceled successfully',
                data: $subscription->load('plan')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to cancel subscription: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Cancel user subscription
     */
    public function cancelUserSubscription(Organization $organization, User $user): Response
    {
        // Check if user belongs to organization
        if (!$organization->hasMember($user)) {
            return ResponseHelper::error(
                message: 'User is not a member of this organization',
                httpCode: 422
            );
        }

        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return ResponseHelper::error(
                message: 'No active subscription found for user',
                httpCode: 404
            );
        }

        try {
            $subscription->cancel();

            return ResponseHelper::success(
                message: 'User subscription canceled successfully',
                data: $subscription->load('plan')
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to cancel user subscription: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Get user's current subscription
     */
    public function userSubscription(Organization $organization, User $user): Response
    {
        // Check if user belongs to organization
        if (!$organization->hasMember($user)) {
            return ResponseHelper::error(
                message: 'User is not a member of this organization',
                httpCode: 422
            );
        }

        $subscription = $user->currentSubscription();

        if (!$subscription) {
            return ResponseHelper::error(
                message: 'No active subscription found for user',
                httpCode: 404
            );
        }

        return ResponseHelper::success(data: $subscription->load('plan'));
    }

    /**
     * Get all organization members with their subscriptions
     */
    public function membersWithSubscriptions(Organization $organization): Response
    {
        $members = $organization->activeUsers()->load(['subscriptions' => function ($query) {
            $query->notCanceled()->with('plan');
        }]);

        return ResponseHelper::success(data: $members);
    }
}
