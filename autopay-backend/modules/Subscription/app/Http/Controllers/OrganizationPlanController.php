<?php

namespace Modules\Subscription\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use Modules\Subscription\Models\Feature;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Subscription\Http\Requests\CreateOrganizationPlanRequest;
use Modules\Subscription\Http\Requests\UpdateOrganizationPlanRequest;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Data\OrganizationPlanData;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing Organization Plans
 */
class OrganizationPlanController extends Controller
{

    /**
     * Get organization plans with pagination
     */
    public function index(Organization $organization, Request $request): Response
    {
        $perPage = $request->get('per_page', 15);
        $plans = Plan::forOrganization($organization->id)
            ->with(['features', 'subscriptions'])
            ->orderBy('created_at', 'asc')
            ->paginate($perPage);

        // Transform paginated data using Data class
        $transformedPlans = $plans->through(fn($plan) => OrganizationPlanData::fromPlan($plan));

        return ResponseHelper::success(data: $transformedPlans);
    }

    /**
     * Get system plans (templates)
     */
    public function systemPlans(): Response
    {
        $plans = Plan::systemPlans()
            ->with('features')
            ->orderBy('created_at', 'desc')
            ->get();

        return ResponseHelper::success(data: $plans);
    }

    /**
     * Create a new organization plan
     */
    public function store(Organization $organization, CreateOrganizationPlanRequest $request): Response
    {
        try {
            $data = $request->validated();

            $planData = [
                'organization_id' => $organization->id,
                'name' => $data['name'],
                'alias' => $data['alias'] ?? null,
                'description' => $data['description'] ?? null,
                'grace_days' => $data['grace_days'] ?? 0,
                'periodicity' => $data['periodicity'] ?? 1,
                'periodicity_type' => $data['periodicity_type'] ?? PeriodicityType::Month,
                'price' => $data['price'] ?? 0,
            ];

            $plan = Plan::create($planData);

            // Auto-attach transactions feature with quota as charges
            $transactionsFeature = Feature::where('alias', 'transactions')->first();
            if ($transactionsFeature) {
                $plan->features()->attach($transactionsFeature->id, [
                    'charges' => $data['quota'] ?? null,
                ]);
            }



            return ResponseHelper::success(
                message: 'Plan created successfully',
                data: OrganizationPlanData::fromPlan($plan->load('features')),
                httpCode: 201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to create plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Get a specific organization plan
     */
    public function show(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        $plan->load(['features', 'subscriptions']);

        return ResponseHelper::success(data: OrganizationPlanData::fromPlan($plan));
    }

    /**
     * Update an organization plan
     */
    public function update(Organization $organization, Plan $plan, UpdateOrganizationPlanRequest $request): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        try {
            $data = $request->validated();

            // Update plan basic info (alias will be auto-generated by model if needed)
            $plan->update($data);

            // Update quota if provided
            if (isset($data['quota'])) {
                $transactionsFeature = Feature::where('alias', 'transactions')->first();
                if ($transactionsFeature) {
                    $plan->features()->updateExistingPivot($transactionsFeature->id, [
                        'charges' => $data['quota'],
                    ]);
                }
            }

            return ResponseHelper::success(
                message: 'Plan updated successfully',
                data: OrganizationPlanData::fromPlan($plan->load('features'))
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to update plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Delete an organization plan
     */
    public function destroy(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        // Check if plan has active subscriptions
        if ($plan->subscriptions()->notCanceled()->exists()) {
            return ResponseHelper::error(
                message: 'Cannot delete plan with active subscriptions',
                httpCode: 422
            );
        }

        try {
            $plan->delete();

            return ResponseHelper::success(message: 'Plan deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to delete plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Toggle plan active status
     */
    public function toggleStatus(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization
        if ($plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        try {
            $plan->update(['is_active' => !$plan->is_active]);

            return ResponseHelper::success(
                message: 'Plan status updated successfully',
                data: OrganizationPlanData::fromPlan($plan->load('features'))
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to update plan status: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Duplicate a plan (create copy)
     */
    public function duplicate(Organization $organization, Plan $plan): Response
    {
        // Ensure plan belongs to organization or is a system plan
        if ($plan->isOrganizationPlan() && $plan->organization_id !== $organization->id) {
            return ResponseHelper::error(
                message: 'Plan not found',
                httpCode: 404
            );
        }

        try {
            $newPlan = Plan::create([
                'organization_id' => $organization->id,
                'name' => $plan->name . ' (Copy)',
                'alias' => $plan->alias ? $plan->alias . '-copy' : null, // Model will auto-generate if null
                'description' => $plan->description,
                'grace_days' => $plan->grace_days,
                'periodicity' => $plan->periodicity,
                'periodicity_type' => $plan->periodicity_type,
                'price' => $plan->price,
                'is_active' => false, // Start as inactive
            ]);

            // Copy features with their pivot data
            foreach ($plan->features as $feature) {
                $newPlan->features()->attach($feature->id, [
                    'charges' => $feature->pivot->charges,
                ]);
            }

            return ResponseHelper::success(
                message: 'Plan duplicated successfully',
                data: OrganizationPlanData::fromPlan($newPlan->load('features')),
                httpCode: 201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Failed to duplicate plan: ' . $e->getMessage(),
                httpCode: 422
            );
        }
    }

    /**
     * Get organization subscribers
     */
    public function subscribers(Organization $organization): Response
    {
        $planIds = Plan::forOrganization($organization->id)->pluck('id');

        $subscribers = User::whereHas('subscriptions', function ($query) use ($planIds) {
            $query->whereIn('plan_id', $planIds)->notCanceled();
        })->with(['subscriptions' => function ($query) use ($planIds) {
            $query->whereIn('plan_id', $planIds)->notCanceled()->with('plan');
        }])->get();

        return ResponseHelper::success(data: $subscribers);
    }
}
