<?php

namespace Modules\Subscription\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Settings\TransactionPricingSettings;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Modules\Core\Helpers\ResponseHelper;
use Symfony\Component\HttpFoundation\Response;

class TransactionPricingController extends Controller
{
    protected TransactionPricingSettings $settings;

    public function __construct(TransactionPricingSettings $settings)
    {
        $this->settings = $settings;
    }

    /**
     * Get transaction pricing tiers
     */
    public function index(): Response
    {
        try {
            $tiers = $this->settings->getPricingTiers();

            return ResponseHelper::success(
                message: 'Lấy bảng giá giao dịch thành công',
                data: [
                    'pricing_tiers' => $tiers,
                ]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi xảy ra khi lấy bảng giá giao dịch',
                data: null,
                httpCode: 500
            );
        }
    }

    /**
     * Calculate cost for given transaction count
     */
    public function calculateCost(Request $request): Response
    {
        try {
            $validated = $request->validate([
                'transaction_count' => 'required|integer|min:0',
            ]);

            $calculation = $this->settings->calculateTotalCost($validated['transaction_count']);

            return ResponseHelper::success(
                message: 'Tính toán chi phí thành công',
                data: $calculation
            );
        } catch (ValidationException $e) {
            return ResponseHelper::error(
                message: 'Dữ liệu không hợp lệ',
                data: $e->errors(),
                httpCode: 422
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                message: 'Có lỗi xảy ra khi tính toán chi phí',
                data: null,
                httpCode: 500
            );
        }
    }
}
