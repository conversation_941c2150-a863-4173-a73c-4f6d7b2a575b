<?php

namespace Modules\Subscription\Http\Controllers;

use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Subscription\Models\Feature;
use Symfony\Component\HttpFoundation\Response;

/**
 * Controller for managing Features
 */
class FeatureController extends Controller
{
    /**
     * Get all available features
     */
    public function index(): Response
    {
        $features = Feature::orderBy('name')->get();

        return ResponseHelper::success(data: $features);
    }

    /**
     * Show a specific feature
     */
    public function show(Feature $feature): Response
    {
        return ResponseHelper::success(data: $feature);
    }
}
