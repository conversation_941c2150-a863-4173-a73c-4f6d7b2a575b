<?php

namespace Modules\Subscription\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Support\Str;
use LucasDotVin\Soulbscription\Models\Feature as BaseFeature;

/**
 * Custom Feature Model extending Soulbscription Feature
 *
 * Adds ULID support for primary keys and auto-generates alias from name
 */
class Feature extends BaseFeature
{
    use HasUlids;

    protected $fillable = [
        'name',
        'alias',
        'consumable',
        'quota',
        'postpaid',
        'periodicity',
        'periodicity_type',
    ];

    /**
     * Boot the model
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(static function ($feature) {
            if (empty($feature->alias)) {
                $feature->alias = static::generateUniqueAlias($feature->name);
            }
        });

        static::updating(static function ($feature) {
            if (empty($feature->alias) && $feature->isDirty('name')) {
                $feature->alias = static::generateUniqueAlias($feature->name);
            }
        });
    }

    /**
     * Generate a unique alias for the feature
     */
    public static function generateUniqueAlias(string $name): string
    {
        $baseAlias = Str::slug($name, '-');

        // Ensure it only contains allowed characters
        $baseAlias = preg_replace('/[^a-z0-9_-]/', '', strtolower($baseAlias));

        $alias = $baseAlias;
        $counter = 1;

        // Check if alias already exists and append a number if needed
        while (static::where('alias', $alias)->exists()) {
            $alias = $baseAlias . '-' . $counter;
            $counter++;
        }

        return $alias;
    }
}
