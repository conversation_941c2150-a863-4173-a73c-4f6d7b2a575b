<?php

namespace Modules\Subscription\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use LucasDotVin\Soulbscription\Models\Plan as BasePlan;
use Modules\Organization\Models\Organization;

/**
 * Custom Plan Model extending Soulbscription Plan
 *
 * Adds organization support for multi-tenant plans:
 * - System Plans: organization_id = null (created by system admin)
 * - Organization Plans: organization_id = specific org (created by org owners)
 */
class Plan extends BasePlan
{
    use HasUlids;

    protected $fillable = [
        'organization_id',
        'name',
        'alias',
        'description',
        'grace_days',
        'periodicity',
        'periodicity_type',
        'price',
        'is_active',
    ];

    protected $casts = [
        'grace_days' => 'integer',
        'periodicity' => 'integer',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'quota',
    ];

    /**
     * Boot the model
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(static function ($plan) {
            if (empty($plan->alias)) {
                $plan->alias = static::generateUniqueAlias($plan->name, $plan->organization_id);
            }
        });

        static::updating(static function ($plan) {
            if (empty($plan->alias) && $plan->isDirty('name')) {
                $plan->alias = static::generateUniqueAlias($plan->name, $plan->organization_id);
            }
        });
    }

    /**
     * Get the organization that owns this plan
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope to get system plans (templates)
     */
    public function scopeSystemPlans(Builder $query): Builder
    {
        return $query->whereNull('organization_id');
    }

    /**
     * Scope to get organization plans
     */
    public function scopeForOrganization(Builder $query, string $organizationId): Builder
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope to get plans available to an organization (system + org plans)
     */
    public function scopeAvailableToOrganization(Builder $query, string $organizationId): Builder
    {
        return $query->where(function ($q) use ($organizationId) {
            $q->whereNull('organization_id') // System plans
              ->orWhere('organization_id', $organizationId); // Organization plans
        });
    }

    /**
     * Check if this is a system plan
     */
    public function isSystemPlan(): bool
    {
        return $this->organization_id === null;
    }

    /**
     * Check if this is an organization plan
     */
    public function isOrganizationPlan(): bool
    {
        return $this->organization_id !== null;
    }

    /**
     * Get plan type for display
     */
    public function getTypeAttribute(): string
    {
        return $this->isSystemPlan() ? 'system' : 'organization';
    }

    /**
     * Get plan owner name for display
     */
    public function getOwnerNameAttribute(): string
    {
        if ($this->isSystemPlan()) {
            return 'System';
        }

        return $this->organization?->name ?? 'Unknown Organization';
    }

    /**
     * Get quota from transactions feature
     */
    public function getQuotaAttribute(): ?int
    {
        $transactionsFeature = $this->features()->where('alias', 'transactions')->first();

        if (!$transactionsFeature) {
            return null;
        }

        return $transactionsFeature->pivot->charges;
    }

    /**
     * Generate a unique alias for the plan within an organization
     */
    public static function generateUniqueAlias(string $name, ?string $organizationId = null): string
    {
        $baseAlias = Str::slug($name, '-');

        // Ensure it only contains allowed characters
        $baseAlias = preg_replace('/[^a-z0-9_-]/', '', strtolower($baseAlias));

        $alias = $baseAlias;
        $counter = 1;

        // Check if alias already exists for this organization
        while (static::where('organization_id', $organizationId)
                     ->where('alias', $alias)
                     ->exists()) {
            $alias = $baseAlias . '-' . $counter;
            $counter++;
        }

        return $alias;
    }
}
