<?php

namespace Modules\Subscription\Policies;

use Modules\Subscription\Models\Plan;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;

/**
 * Policy for Plan authorization
 */
class PlanPolicy
{
    /**
     * Determine if the user can view any plans for the organization
     */
    public function viewAny(User $user, Organization $organization): bool
    {
        return $organization->hasMember($user);
    }

    /**
     * Determine if the user can view a specific plan
     */
    public function view(User $user, Plan $plan, Organization $organization): bool
    {
        // System plans can be viewed by any organization member
        if ($plan->isSystemPlan()) {
            return $organization->hasMember($user);
        }

        // Organization plans can only be viewed by members of that organization
        return $plan->organization_id === $organization->id && $organization->hasMember($user);
    }

    /**
     * Determine if the user can create plans for the organization
     */
    public function create(User $user, Organization $organization): bool
    {
        // Only organization owners and admins can create plans
        return $organization->isOwner($user) || 
               $organization->userHasPermission($user, 'manage_plans');
    }

    /**
     * Determine if the user can update the plan
     */
    public function update(User $user, Plan $plan, Organization $organization): bool
    {
        // System plans cannot be updated by organization users
        if ($plan->isSystemPlan()) {
            return false;
        }

        // Organization plans can only be updated by owners/admins of that organization
        return $plan->organization_id === $organization->id && 
               ($organization->isOwner($user) || $organization->userHasPermission($user, 'manage_plans'));
    }

    /**
     * Determine if the user can delete the plan
     */
    public function delete(User $user, Plan $plan, Organization $organization): bool
    {
        // System plans cannot be deleted by organization users
        if ($plan->isSystemPlan()) {
            return false;
        }

        // Organization plans can only be deleted by owners/admins of that organization
        return $plan->organization_id === $organization->id && 
               ($organization->isOwner($user) || $organization->userHasPermission($user, 'manage_plans'));
    }

    /**
     * Determine if the user can duplicate the plan
     */
    public function duplicate(User $user, Plan $plan, Organization $organization): bool
    {
        // Can duplicate system plans or organization plans that belong to the organization
        if ($plan->isSystemPlan()) {
            return $organization->isOwner($user) || $organization->userHasPermission($user, 'manage_plans');
        }

        return $plan->organization_id === $organization->id && 
               ($organization->isOwner($user) || $organization->userHasPermission($user, 'manage_plans'));
    }

    /**
     * Determine if the user can subscribe to the plan
     */
    public function subscribe(User $user, Plan $plan, Organization $organization): bool
    {
        // System plans: only organization owners can subscribe the organization
        if ($plan->isSystemPlan()) {
            return $organization->isOwner($user);
        }

        // Organization plans: organization members can subscribe
        return $plan->organization_id === $organization->id && $organization->hasMember($user);
    }

    /**
     * Determine if the user can manage subscriptions for the organization
     */
    public function manageSubscriptions(User $user, Organization $organization): bool
    {
        // Organization owners and admins can manage subscriptions
        return $organization->isOwner($user) || 
               $organization->userHasPermission($user, 'manage_subscriptions');
    }

    /**
     * Determine if the user can view plan usage statistics
     */
    public function viewUsageStats(User $user, Organization $organization): bool
    {
        // Organization owners and admins can view usage stats
        return $organization->isOwner($user) || 
               $organization->userHasPermission($user, 'view_analytics');
    }

    /**
     * Determine if the user can view plan subscribers
     */
    public function viewSubscribers(User $user, Organization $organization): bool
    {
        // Organization owners and admins can view subscribers
        return $organization->isOwner($user) || 
               $organization->userHasPermission($user, 'manage_members');
    }
}
