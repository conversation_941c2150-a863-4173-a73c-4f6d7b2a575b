<?php

namespace Modules\Subscription\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class FeatureData extends Data
{
    public function __construct(
        public string $id,
        public string $name,
        public ?string $alias,
        public bool $consumable,
        public bool $quota,
        public bool $postpaid,
        public PivotData $pivot
    ) {}

    /**
     * Create from Feature model with pivot data
     */
    public static function fromFeatureWithPivot($feature): self
    {
        return new self(
            id: $feature->id,
            name: $feature->name,
            alias: $feature->alias,
            consumable: $feature->consumable,
            quota: $feature->quota,
            postpaid: $feature->postpaid,
            pivot: PivotData::fromPivot($feature->pivot)
        );
    }
}
