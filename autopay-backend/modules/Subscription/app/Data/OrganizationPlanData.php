<?php

namespace Modules\Subscription\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON>tie\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class OrganizationPlanData extends Data
{
    public function __construct(
        public string $id,
        public string $name,
        public ?string $alias,
        public ?string $description,
        public int $grace_days,
        public int $periodicity,
        public string $periodicity_type,
        public float $price,
        public string $organization_id,
        public bool $is_active,
        public ?int $quota,
        public array $features,
        public ?array $subscriptions,
        public string $created_at,
        public string $updated_at
    ) {}

    /**
     * Create from Plan model
     */
    public static function fromPlan($plan): self
    {
        return new self(
            id: $plan->id,
            name: $plan->name ?? '',
            alias: $plan->alias,
            description: $plan->description,
            grace_days: $plan->grace_days ?? 0,
            periodicity: $plan->periodicity ?? 1,
            periodicity_type: $plan->periodicity_type ?? 'month',
            price: $plan->price ?? 0.0,
            organization_id: $plan->organization_id,
            is_active: $plan->is_active ?? false,
            quota: $plan->quota,
            features: $plan->features->map(fn($feature) => FeatureData::fromFeatureWithPivot($feature)->toArray())->toArray(),
            subscriptions: $plan->subscriptions ? $plan->subscriptions->map(fn($subscription) => SubscriptionData::fromSubscription($subscription)->toArray())->toArray() : null,
            created_at: $plan->created_at->toISOString(),
            updated_at: $plan->updated_at->toISOString()
        );
    }
}
