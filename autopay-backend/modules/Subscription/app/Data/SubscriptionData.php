<?php

namespace Modules\Subscription\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class SubscriptionData extends Data
{
    public function __construct(
        public string $id,
        public string $subscriber_id,
        public string $subscriber_type
    ) {}

    /**
     * Create from Subscription model
     */
    public static function fromSubscription($subscription): self
    {
        return new self(
            id: $subscription->id,
            subscriber_id: $subscription->subscriber_id,
            subscriber_type: $subscription->subscriber_type
        );
    }
}
