<?php

namespace Modules\Subscription\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class PivotData extends Data
{
    public function __construct(
        public ?int $charges
    ) {}

    /**
     * Create from pivot data
     */
    public static function fromPivot($pivot): self
    {
        return new self(
            charges: $pivot?->charges
        );
    }
}
