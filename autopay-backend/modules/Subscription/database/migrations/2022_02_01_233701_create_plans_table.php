<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', static function (Blueprint $table) {
            $table->ulid('id')->primary();

            // Add organization_id to distinguish between system plans and organization plans
            $table->foreignUlid('organization_id')
                ->nullable()
                ->constrained('organizations')
                ->cascadeOnDelete();

            $table->integer('grace_days')->default(0);
            $table->string('name');
            $table->string('alias');

            // Add a description field for better plan management
            $table->text('description')->nullable();

            $table->integer('periodicity')->unsigned()->nullable();
            $table->string('periodicity_type')->nullable();

            // Add a price field for plan pricing
            $table->unsignedBigInteger('price')->default(0);

            $table->boolean('is_active')->default(true);
            $table->softDeletes();
            $table->timestamps();

            // Add indexes for performance
            $table->index(['organization_id']);
            $table->index(['organization_id', 'name']);
            $table->index(['organization_id', 'created_at']);

            // Create unique constraint for plan names within the same organization
            // System plans (organization_id = null) can have the same names as org plans
            $table->unique(['organization_id', 'name'], 'plans_org_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
