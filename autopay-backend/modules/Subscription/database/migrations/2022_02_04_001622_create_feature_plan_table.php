<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;
use Modules\Subscription\Models\Feature;
use Modules\Subscription\Models\Plan;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feature_plan', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->decimal('charges')->nullable();
            $table->foreignUlid('feature_id')->constrained('features')->cascadeOnDelete();
            $table->foreignUlid('plan_id')->constrained('plans')->cascadeOnDelete();
            $table->timestamps();
        });


        // Create default features and plans
        $this->createDefaultFeaturesAndPlans();
    }



    /**
     * Create default features and system plans
     */
    private function createDefaultFeaturesAndPlans(): void
    {
        // Check if transactions feature already exists
        $transactions = Feature::where('alias', 'transactions')->first();
        if (!$transactions) {
            $transactions = Feature::create([
                'id' => \Illuminate\Support\Str::ulid(),
                'name' => 'Giao dịch',
                'alias' => 'transactions',
                'consumable' => true,
                'postpaid' => true,
                'periodicity_type' => PeriodicityType::Month,
                'periodicity' => 1,
            ]);
        }

        // Check if freemium plan already exists
        $free = Plan::where('alias', 'freemium')->whereNull('organization_id')->first();
        if (!$free) {
            $free = Plan::create([
                'id' => \Illuminate\Support\Str::ulid(),
                'organization_id' => null, // System plan
                'name' => 'Miễn phí',
                'alias' => 'freemium',
                'description' => 'Gói dịch vụ miễn phí với số lượng giao dịch giới hạn cho mục đích thử nghiệm',
                'periodicity_type' => PeriodicityType::Month,
                'periodicity' => 1,
                'price' => 0,
            ]);

            $free->features()->attach($transactions, [
                'charges' => 50, // 50 transactions per month
            ]);
        }

        // Check if pay-as-you-go plan already exists
        $starter = Plan::where('alias', 'pay-as-you-go')->whereNull('organization_id')->first();
        if (!$starter) {
            $starter = Plan::create([
                'id' => \Illuminate\Support\Str::ulid(),
                'organization_id' => null, // System plan
                'name' => 'Thanh toán theo mức sử dụng',
                'alias' => 'pay-as-you-go',
                'description' => 'Gói dịch vụ thanh toán theo mức sử dụng thực tế',
                'periodicity_type' => PeriodicityType::Month,
                'periodicity' => 1,
                'grace_days' => 1,
                'price' => 50000,
            ]);

            $starter->features()->attach($transactions, [
                'charges' => 0, // Unlimited transactions (postpaid billing)
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feature_plan');
    }
};
