<?php

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class TransactionPricingSettings extends Settings
{
    public array $pricing_tiers;

    public static function group(): string
    {
        return 'transaction_pricing';
    }

    /**
     * Get all pricing tiers
     */
    public function getPricingTiers(): array
    {
        return $this->pricing_tiers ?? $this->getDefaultPricingTiers();
    }

    /**
     * Set pricing tiers
     */
    public function setPricingTiers(array $tiers): void
    {
        $this->pricing_tiers = $tiers;
        $this->save();
    }

    /**
     * Get default pricing tiers
     */
    public function getDefaultPricingTiers(): array
    {
        return [
            [
                'name' => 'Miễn phí',
                'name_en' => 'Free',
                'min_transactions' => 0,
                'max_transactions' => 100,
                'price_per_transaction' => 0,
                'color_class' => 'green',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Cơ bản',
                'name_en' => 'Basic',
                'min_transactions' => 101,
                'max_transactions' => 500,
                'price_per_transaction' => 500,
                'color_class' => 'default',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Tiết kiệm',
                'name_en' => 'Economy',
                'min_transactions' => 501,
                'max_transactions' => 1000,
                'price_per_transaction' => 400,
                'color_class' => 'default',
                'is_unlimited' => false,
            ],
            [
                'name' => 'Ưu đãi',
                'name_en' => 'Premium',
                'min_transactions' => 1001,
                'max_transactions' => null,
                'price_per_transaction' => 300,
                'color_class' => 'default',
                'is_unlimited' => true,
            ],
        ];
    }

    /**
     * Calculate total cost for given number of transactions
     */
    public function calculateTotalCost(int $transactionCount): array
    {
        $tiers = $this->getPricingTiers();
        $totalCost = 0;
        $breakdown = [];

        foreach ($tiers as $tier) {
            $tierMin = $tier['min_transactions'];
            $tierMax = $tier['max_transactions'];

            // Skip if transaction count doesn't reach this tier
            if ($transactionCount < $tierMin) {
                break;
            }

            // Calculate how many transactions fall into this tier
            $transactionsInTier = 0;

            if ($tier['is_unlimited'] || $tierMax === null) {
                // This is the unlimited tier - all remaining transactions
                $transactionsInTier = $transactionCount - $tierMin + 1;
            } else {
                // Calculate transactions in this tier range
                $tierEnd = min($tierMax, $transactionCount);
                $transactionsInTier = $tierEnd - $tierMin + 1;
            }

            if ($transactionsInTier > 0) {
                $tierCost = $transactionsInTier * $tier['price_per_transaction'];
                $totalCost += $tierCost;

                $breakdown[] = [
                    'tier_name' => $tier['name'],
                    'transactions' => $transactionsInTier,
                    'price_per_transaction' => $tier['price_per_transaction'],
                    'tier_cost' => $tierCost,
                ];
            }
        }

        return [
            'total_cost' => $totalCost,
            'breakdown' => $breakdown,
            'total_transactions' => $transactionCount,
        ];
    }

    /**
     * Get pricing tier for specific transaction count
     */
    public function getTierForTransactionCount(int $transactionCount): ?array
    {
        $tiers = $this->getPricingTiers();

        foreach ($tiers as $tier) {
            $min = $tier['min_transactions'];
            $max = $tier['max_transactions'];

            if ($transactionCount >= $min && ($max === null || $transactionCount <= $max)) {
                return $tier;
            }
        }

        return null;
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults(): void
    {
        if (empty($this->pricing_tiers)) {
            $this->pricing_tiers = $this->getDefaultPricingTiers();
            $this->save();
        }
    }
}
